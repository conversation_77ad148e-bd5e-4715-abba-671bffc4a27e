import Popper from '@mui/material/Popper'
import useTheme from '@mui/material/styles/useTheme'
import { makeStyles } from '@mui/styles'
import { AnimatePresence, motion } from 'framer-motion'
import { useEffect, useRef, useState } from 'react'

import MentionCard from './MentionCard'
import { MENTION_MARKUP_EVERYONE } from '@memberup/shared/src/types/consts'
import { IUser } from '@memberup/shared/src/types/interfaces'
import { getFullName } from '@/shared-libs/profile'

const useStyles = makeStyles((theme) => ({
  mention: {
    fontWeight: 'bold',
    transition: 'color 2s ease',
  },
}))

function RenderMention({
  isEveryone,
  userData,
  viewType = 'single',
}: {
  isEveryone: boolean
  userData: IUser | null | { first_name: string; last_name: string }
  viewType?: 'condensed' | 'single' | 'comment'
}) {
  const theme = useTheme()

  const classes = useStyles()
  const [anchorEl, setAnchorEl] = useState(null)

  const [isAnimatingOut, setIsAnimatingOut] = useState(false)
  const hoverTimerRef = useRef(null)

  const handleMouseEnter = (e) => {
    if (!isEveryone || viewType !== 'condensed') {
      // Clear any existing timer to prevent duplicates
      if (hoverTimerRef.current) clearTimeout(hoverTimerRef.current)
      const currentTarget = e.currentTarget
      hoverTimerRef.current = setTimeout(() => {
        setAnchorEl(currentTarget)
        setIsAnimatingOut(false)
      }, 800) // Wait for 2 seconds before showing the MentionCard
    }
  }

  const handleMouseLeave = (e) => {
    if (!isEveryone || viewType !== 'condensed') {
      // Clear the timer when the mouse leaves
      if (hoverTimerRef.current) {
        clearTimeout(hoverTimerRef.current)
        hoverTimerRef.current = null
      }
      setIsAnimatingOut(true)
    }
  }

  useEffect(() => {
    if (isAnimatingOut) {
      const timer = setTimeout(() => {
        setAnchorEl(null)
      }, 2300) // Match this with your animation duration

      return () => clearTimeout(timer)
    }
  }, [isAnimatingOut])

  useEffect(() => {
    return () => {
      if (hoverTimerRef.current) clearTimeout(hoverTimerRef.current)
    }
  }, [])

  const open = Boolean(anchorEl)
  const id = open ? 'simple-poper' : undefined

  const fadeOut = {
    opacity: 0,
    y: -10,
    transition: { duration: 0.5 },
  }

  const renderContent = () => {
    return (
      <>
        <span
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
          className={classes.mention}
          style={{
            color: theme.palette.primary.main,
            cursor: !isEveryone ? 'pointer' : '',
            fontFamily: 'Graphik Semibold',
            fontWeight: 400,
          }}
        >
          @{getFullName(userData.first_name, userData.last_name, '')}
        </span>
        {!isEveryone && viewType !== 'condensed' && (
          <AnimatePresence>
            {open && (
              <Popper id={id} open={open} anchorEl={anchorEl} style={{ zIndex: 9999 }}>
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={fadeOut}
                  transition={{ duration: 0.5 }}
                >
                  <MentionCard userData={userData} />
                </motion.div>
              </Popper>
            )}
          </AnimatePresence>
        )}{' '}
      </>
    )
  }
  return renderContent()
}

export default RenderMention
